# tests/hardware/test_robot_status.py

import sys
import os

# 将项目根目录添加到Python路径中
PROJECT_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../'))
sys.path.insert(0, PROJECT_ROOT)

# 将 lib 目录也明确添加到路径中
LIB_PATH = os.path.join(PROJECT_ROOT, 'lib')
if LIB_PATH not in sys.path:
    sys.path.insert(0, LIB_PATH)

# 导入配置和接口
from config import ROBOT_IP, ROBOT_PORT
from src.hardware.robot_interface import RobotInterface

# 导入nrc_interface
try:
    import nrc_interface as nrc
    print("成功导入nrc_interface模块")
except ImportError as e:
    print(f"导入nrc_interface失败: {e}")
    sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../lib')))
    try:
        import nrc_interface as nrc
        print("通过绝对路径成功导入nrc_interface模块")
    except ImportError as e:
        print(f"再次尝试导入失败: {e}")
        print("请确保lib目录中包含nrc_interface.py和nrc_host.pyd文件")

class RobotStatusReader:
    """机械臂状态读取器 - 读取所有可用的机器人状态信息"""

    def __init__(self, robot_interface: RobotInterface):
        self.robot = robot_interface
        self.socket_fd = robot_interface.socket_fd

    def _safe_api_call(self, api_func, *args):
        """安全调用API并格式化输出"""
        try:
            result = api_func(self.socket_fd, *args)
            if isinstance(result, list) and len(result) > 1:
                return f"{result[1]} (返回码: {result[0]})"
            else:
                return f"{result}"
        except Exception as e:
            return f"读取失败 - {e}"

    def read_basic_status(self):
        """读取基本状态信息"""
        print("\n=== 基本状态信息 ===")

        # 连接状态
        conn_status = nrc.get_connection_status(self.socket_fd)
        print(f"连接状态: {conn_status}")

        # 伺服状态
        servo_status = 0
        result = self._safe_api_call(nrc.get_servo_state, servo_status)
        print(f"伺服状态: {result}")

        # 机器人运行状态
        running_status = 0
        result = self._safe_api_call(nrc.get_robot_running_state, running_status)
        print(f"运行状态: {result}")

        # 当前速度
        speed = 0
        result = self._safe_api_call(nrc.get_speed, speed)
        print(f"当前速度: {result}")

        # 当前模式
        mode = 0
        result = self._safe_api_call(nrc.get_current_mode, mode)
        print(f"当前模式: {result}")

    def read_position_info(self):
        """读取位置信息"""
        print("\n=== 位置信息 ===")

        # 当前坐标系
        coord = 0
        result = self._safe_api_call(nrc.get_current_coord, coord)
        print(f"当前坐标系: {result}")

        # 当前工具手编号
        tool_num = 0
        result = self._safe_api_call(nrc.get_tool_hand_number, tool_num)
        print(f"当前工具手编号: {result}")

        # 当前位置 (关节坐标系)
        try:
            from nrc_interface import VectorDouble
            joint_pos = VectorDouble()
            ret_code = nrc.get_current_position(self.socket_fd, 0, joint_pos)
            joint_pos_list = [joint_pos[i] for i in range(len(joint_pos))]
            print(f"当前关节位置: {joint_pos_list} (返回码: {ret_code})")
        except Exception as e:
            print(f"读取关节位置失败: {e}")

        # 当前位置 (笛卡尔坐标系)
        try:
            cart_pos = VectorDouble()
            ret_code = nrc.get_current_position(self.socket_fd, 1, cart_pos)
            cart_pos_list = [cart_pos[i] for i in range(len(cart_pos))]
            print(f"当前笛卡尔位置: {cart_pos_list} (返回码: {ret_code})")
        except Exception as e:
            print(f"读取笛卡尔位置失败: {e}")


    def read_motor_status(self):
        """读取电机状态信息"""
        print("\n=== 电机状态信息 ===")

        # 电机扭矩
        try:
            from nrc_interface import VectorInt, VectorDouble
            motor_torque = VectorInt()
            motor_torque_sync = VectorInt()
            ret_code = nrc.get_curretn_motor_torque(self.socket_fd, motor_torque, motor_torque_sync)
            torque_list = [motor_torque[i] for i in range(len(motor_torque))]
            print(f"电机扭矩: {torque_list} (返回码: {ret_code})")
        except Exception as e:
            print(f"读取电机扭矩失败: {e}")

        # 电机转速
        try:
            motor_speed = VectorInt()
            motor_speed_sync = VectorInt()
            ret_code = nrc.get_curretn_motor_speed(self.socket_fd, motor_speed, motor_speed_sync)
            speed_list = [motor_speed[i] for i in range(len(motor_speed))]
            print(f"电机转速: {speed_list} (返回码: {ret_code})")
        except Exception as e:
            print(f"读取电机转速失败: {e}")

        # 电机负载
        try:
            motor_payload = VectorDouble()
            motor_payload_sync = VectorDouble()
            ret_code = nrc.get_curretn_motor_payload(self.socket_fd, motor_payload, motor_payload_sync)
            payload_list = [motor_payload[i] for i in range(len(motor_payload))]
            print(f"电机负载: {payload_list} (返回码: {ret_code})")
        except Exception as e:
            print(f"读取电机负载失败: {e}")

    def read_sensor_data(self):
        """读取传感器数据"""
        print("\n=== 传感器数据 ===")

        try:
            from nrc_interface import VectorInt
            sensor_data = VectorInt()
            ret_code = nrc.get_sensor_data(self.socket_fd, sensor_data)
            sensor_list = [sensor_data[i] for i in range(len(sensor_data))]
            if len(sensor_list) > 0:
                print(f"六维力传感器数据: {sensor_list} (返回码: {ret_code})")
            else:
                print(f"六维力传感器数据: 无数据 (返回码: {ret_code})")
        except Exception as e:
            print(f"读取六维力传感器数据失败: {e}")

    def read_controller_info(self):
        """读取控制器信息"""
        print("\n=== 控制器信息 ===")

        # 控制器ID
        try:
            controller_id = ""
            result = nrc.get_controller_id(self.socket_fd, controller_id)
            if isinstance(result, list) and len(result) > 1:
                print(f"控制器ID: {result[1]} (返回码: {result[0]})")
            else:
                print(f"控制器ID: {result}")
        except Exception as e:
            print(f"读取控制器ID失败: {e}")

        # 库版本
        try:
            version = nrc.get_library_version()
            print(f"SDK库版本: {version}")
        except Exception as e:
            print(f"读取库版本失败: {e}")

    def read_io_status(self):
        """读取IO状态"""
        print("\n=== IO状态信息 ===")

        # 数字输入状态
        try:
            from nrc_interface import VectorInt, VectorDouble
            digital_inputs = VectorInt()
            nrc.get_digital_input(self.socket_fd, digital_inputs)
            for port in range(min(8, len(digital_inputs))):
                print(f"  DI{port}: {digital_inputs[port]}")
            if len(digital_inputs) == 0:
                print("  无数字输入数据")
        except Exception as e:
            print(f"  数字输入读取失败: {e}")

        # 模拟输入状态
        try:
            ain_data = VectorDouble()
            nrc.get_analog_input(self.socket_fd, ain_data)
            for port in range(min(4, len(ain_data))):
                print(f"  AI{port}: {ain_data[port]}")
            if len(ain_data) == 0:
                print("  无模拟输入数据")
        except Exception as e:
            print(f"  模拟输入读取失败: {e}")



    def robot_initialize_and_power_on(self):
        """机器人初始化和上电流程"""
        import time

        print("开始机器人初始化和上电流程...")
        servo_names = {0: "停止状态", 1: "就绪状态", 2: "报警状态", 3: "运行状态"}

        try:
            # 清除错误
            print("步骤 1: 清除错误...")
            nrc.clear_error(self.socket_fd)
            time.sleep(0.5)

            # 获取当前伺服状态
            servo_status = 0
            result = nrc.get_servo_state(self.socket_fd, servo_status)
            if isinstance(result, list) and len(result) > 1:
                current_state = result[1]
            else:
                current_state = servo_status

            print(f"步骤 2: 当前伺服状态为 {current_state} ({servo_names.get(current_state, '未知状态')})")

            # 根据状态执行相应操作
            if current_state == 0:  # 停止状态
                nrc.set_servo_state(self.socket_fd, 1)
                time.sleep(0.5)
            elif current_state == 3:  # 运行状态
                nrc.set_servo_poweroff(self.socket_fd)
                time.sleep(1)

            # 执行上电
            print("步骤 3: 执行上电操作...")
            result = nrc.set_servo_poweron(self.socket_fd)
            if result != 0:
                print(f"上电失败！返回码: {result}")
                print("请检查安全回路、示教器模式、急停按钮等")
                return False

            time.sleep(1)
            print("✅ 机器人上电成功！")
            return True

        except Exception as e:
            print(f"初始化和上电流程失败: {e}")
            return False

    def test_robot_enable_sequence(self):
        """测试机器人使能序列"""
        print("\n=== 机器人使能序列测试 ===")
        print("注意：请确保在示教器上已设置伺服就绪并选择远程模式")

        try:
            # 检查当前模式
            mode = 0
            result = self._safe_api_call(nrc.get_current_mode, mode)
            print(f"当前模式: {result}")

            # 检查伺服状态
            servo_status = 0
            result = self._safe_api_call(nrc.get_servo_state, servo_status)
            print(f"当前伺服状态: {result}")

            # 执行初始化和上电流程
            print("执行机器人上电...")
            success = self.robot_initialize_and_power_on()

            if success:
                print("✅ 机器人初始化和上电成功！")
                return True
            else:
                print("❌ 机器人初始化和上电失败。")
                return False

        except Exception as e:
            print(f"使能序列测试失败: {e}")
            return False


def test_robot_status_reading():
    """测试机器人状态读取功能"""
    robot = None
    try:
        print("=" * 60)
        print("机器人状态读取测试")
        print("=" * 60)

        # 连接机器人
        print(f"正在连接机器人 {ROBOT_IP}:{ROBOT_PORT}...")
        robot = RobotInterface(ROBOT_IP, ROBOT_PORT)

        # 创建状态读取器
        status_reader = RobotStatusReader(robot)

        # 读取各种状态信息
        status_reader.read_basic_status()

        # 测试机器人使能序列
        status_reader.test_robot_enable_sequence()

        # 继续读取其他状态
        status_reader.read_position_info()
        status_reader.read_robot_config()
        status_reader.read_motor_status()
        status_reader.read_sensor_data()
        status_reader.read_teachbox_status()
        status_reader.read_controller_info()
        status_reader.read_io_status()
        status_reader.read_dh_params()
        status_reader.read_joint_params()
        status_reader.read_single_cycle()

        print("\n" + "=" * 60)
        print("状态读取测试完成！")
        print("=" * 60)

    except RuntimeError as e:
        print(f"\n❌ 连接失败（这在没有实际硬件时是正常的）: {e}")
        print("测试框架工作正常，但需要实际的机器人硬件才能完成状态读取测试。")

    except Exception as e:
        print(f"\n❌ 发生意外错误: {e}")
        import traceback
        traceback.print_exc()

    finally:
        if robot:
            robot.disconnect()

if __name__ == "__main__":
    test_robot_status_reading()
